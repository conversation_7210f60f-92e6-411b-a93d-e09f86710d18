#!/usr/bin/env python3
"""
Debug script to test team creation endpoint
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/v1"

def test_team_creation():
    """Test team creation with different scenarios"""
    
    # 1. <PERSON>gin to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    print("1. Logging in...")
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    print(f"✓ Login successful")
    
    # 2. Test get existing teams first
    print("\n2. Getting existing teams...")
    response = requests.get(f"{BASE_URL}/teams", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # 3. Test team creation with different data
    test_cases = [
        {
            "name": "Test Team 1",
            "description": "A test team created by API test"
        },
        {
            "name": "Short Team",
            "description": "Short desc"
        },
        {
            "name": "A" * 100,  # Very long name
            "description": "Test with very long name"
        },
        {
            "name": "",  # Empty name
            "description": "Test with empty name"
        },
        {
            "name": "Valid Team Name"
            # Missing description
        },
        {
            "name": "Team with Special Chars !@#$%",
            "description": "Testing special characters"
        }
    ]
    
    for i, team_data in enumerate(test_cases, 1):
        print(f"\n3.{i}. Testing team creation: {team_data.get('name', 'NO_NAME')[:30]}...")
        response = requests.post(f"{BASE_URL}/teams", json=team_data, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ Team creation successful!")
            # Try to get the team details
            team_response = response.json()
            if 'data' in team_response and 'id' in team_response['data']:
                team_id = team_response['data']['id']
                print(f"Created team ID: {team_id}")
                
                # Test selecting the team
                print(f"   Testing team selection...")
                select_response = requests.post(f"{BASE_URL}/auth/select-team", 
                                              json={"teamId": team_id}, headers=headers)
                print(f"   Select status: {select_response.status_code}")
                if select_response.status_code == 200:
                    team_token = select_response.json()['data']['token']
                    team_headers = {"Authorization": f"Bearer {team_token}"}
                    
                    # Test getting current team details
                    print(f"   Testing get current team...")
                    current_response = requests.get(f"{BASE_URL}/teams/current", headers=team_headers)
                    print(f"   Current team status: {current_response.status_code}")
                    print(f"   Current team response: {current_response.text}")
                
                break  # Stop after first successful creation
        else:
            print(f"✗ Team creation failed")

if __name__ == "__main__":
    test_team_creation()
