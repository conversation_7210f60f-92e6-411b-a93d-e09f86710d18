#!/usr/bin/env python3
"""
Test the mapper method directly
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/v1"

def test_mapper_direct():
    """Test the mapper method directly"""
    
    # Login first
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test the direct mapper method
    print("Testing direct mapper method...")
    response = requests.get(f"{BASE_URL}/friends/test/mapper", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

if __name__ == "__main__":
    test_mapper_direct()
