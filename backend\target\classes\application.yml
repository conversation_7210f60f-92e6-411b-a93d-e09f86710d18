server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: team-manage

  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: ****************************************************************************************************************
    username: root
    password: a228702862.
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Caffeine缓存配置（通过Java配置类管理）
  cache:
    type: caffeine

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:/mapper/**/*.xml
  type-handlers-package: com.teammanage.config

jwt:
  secret: ${JWT_SECRET:mySecretKeyForTeamManageSystemVeryLongAndSecure123456789}
  token-expiration: 604800  # 7天 (秒)

logging:
  level:
    com.teammanage: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: alpha
    tags-sorter: alpha

# 应用自定义配置
app:
  # 会话管理配置
  session:
    max-concurrent-sessions: 5  # 单用户最大并发会话数
    cleanup-interval: 3600      # 会话清理间隔(秒)

  # 安全配置
  security:
    password-min-length: 8
    max-login-attempts: 5
    lockout-duration: 1800      # 账号锁定时长(秒)

  # 团队配置
  team:
    max-members: 100            # 团队最大成员数
    name-max-length: 100

  # 邀请配置
  invitation:
    expire-hours: 72            # 邀请链接有效期(小时)
