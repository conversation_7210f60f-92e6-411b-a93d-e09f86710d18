#!/usr/bin/env python3
"""
Debug script to test specific friend management endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/v1"

def test_friend_endpoints():
    """Test friend management endpoints step by step"""
    
    # 1. <PERSON>gin to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    print("1. Logging in...")
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    print(f"✓ Login successful, token: {token[:50]}...")
    
    # 2. Send a friend request first
    print("\n2. Sending friend request...")
    friend_data = {"email": "<EMAIL>"}
    response = requests.post(f"{BASE_URL}/friends/request", json=friend_data, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # 3. Test get sent friend requests
    print("\n3. Getting sent friend requests...")
    response = requests.get(f"{BASE_URL}/friends/requests/sent", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # 4. Login as another user to test received requests
    print("\n4. Testing with another user...")
    login_data2 = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data2)
    if response.status_code == 200:
        token2 = response.json()['data']['token']
        headers2 = {"Authorization": f"Bearer {token2}"}
        print(f"✓ Second user login successful")
        
        # Test get received friend requests
        print("\n5. Getting received friend requests...")
        response = requests.get(f"{BASE_URL}/friends/requests/received", headers=headers2)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # Test friends list
        print("\n6. Getting friends list...")
        response = requests.get(f"{BASE_URL}/friends/list", headers=headers2)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
    else:
        print(f"Second user login failed: {response.text}")

if __name__ == "__main__":
    test_friend_endpoints()
