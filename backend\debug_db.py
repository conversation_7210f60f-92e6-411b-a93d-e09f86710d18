#!/usr/bin/env python3
"""
Debug script to check database schema
"""

import mysql.connector
from mysql.connector import <PERSON>rror

def check_database():
    """Check database connection and table structure"""
    try:
        # Connect to database
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            database='team_manage',
            user='root',
            password='a228702862.'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Check account_relation table structure
            print("1. Checking account_relation table structure...")
            cursor.execute("DESCRIBE account_relation")
            columns = cursor.fetchall()
            
            for column in columns:
                print(f"   {column[0]}: {column[1]} (Null: {column[2]}, Key: {column[3]}, Default: {column[4]})")
            
            # Check if there are any records
            print("\n2. Checking existing records...")
            cursor.execute("SELECT COUNT(*) FROM account_relation")
            count = cursor.fetchone()[0]
            print(f"   Total records: {count}")
            
            # Check account table
            print("\n3. Checking account table...")
            cursor.execute("SELECT id, email, name FROM account LIMIT 5")
            accounts = cursor.fetchall()
            
            for account in accounts:
                print(f"   ID: {account[0]}, Email: {account[1]}, Name: {account[2]}")
            
            # Try to insert a test record
            print("\n4. Testing insert operation...")
            try:
                insert_query = """
                INSERT INTO account_relation 
                (account_id, invited_by, invited_at, status, requested_at, is_active, is_deleted, created_at, updated_at) 
                VALUES (%s, %s, NOW(), %s, NOW(), %s, %s, NOW(), NOW())
                """
                cursor.execute(insert_query, (1, 2, 'pending', False, False))
                connection.commit()
                print("   Insert successful!")
                
                # Delete the test record
                cursor.execute("DELETE FROM account_relation WHERE account_id = 1 AND invited_by = 2")
                connection.commit()
                print("   Test record deleted")
                
            except Error as e:
                print(f"   Insert failed: {e}")
                connection.rollback()
            
    except Error as e:
        print(f"Database connection failed: {e}")
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    check_database()
