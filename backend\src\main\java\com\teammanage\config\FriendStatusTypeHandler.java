package com.teammanage.config;

import com.teammanage.entity.FriendStatus;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * FriendStatus枚举类型处理器
 * 用于MyBatis在数据库和Java枚举之间进行转换
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@MappedTypes(FriendStatus.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class FriendStatusTypeHandler extends BaseTypeHandler<FriendStatus> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, FriendStatus parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.getCode());
    }

    @Override
    public FriendStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String code = rs.getString(columnName);
        return code == null ? null : FriendStatus.fromCode(code);
    }

    @Override
    public FriendStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String code = rs.getString(columnIndex);
        return code == null ? null : FriendStatus.fromCode(code);
    }

    @Override
    public FriendStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String code = cs.getString(columnIndex);
        return code == null ? null : FriendStatus.fromCode(code);
    }
}
