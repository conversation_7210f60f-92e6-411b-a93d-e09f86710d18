#!/usr/bin/env python3
"""
Debug script to test enum values
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/v1"

def test_enum_values():
    """Test what enum values are being used"""
    
    # Login first
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code != 200:
        print(f"Login failed: {response.text}")
        return
    
    token = response.json()['data']['token']
    headers = {"Authorization": f"Bearer {token}"}
    
    # Try to send a friend request to see what gets stored
    print("1. Sending friend <NAME_EMAIL>...")
    friend_data = {"email": "<EMAIL>"}
    response = requests.post(f"{BASE_URL}/friends/request", json=friend_data, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Now try the problematic endpoints with more detailed error info
    print("\n2. Testing sent requests endpoint...")
    response = requests.get(f"{BASE_URL}/friends/requests/sent", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    
    # Check if we can get any debug info from the backend logs
    # Let's also try a different approach - test with raw SQL
    
if __name__ == "__main__":
    test_enum_values()
