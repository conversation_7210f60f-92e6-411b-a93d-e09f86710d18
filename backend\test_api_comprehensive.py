#!/usr/bin/env python3
"""
Comprehensive API Test Script for teamAuth Backend
Tests all available endpoints with proper authentication and error handling.
"""

import requests
import json
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('api_test_results.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TeamAuthAPITester:
    """Comprehensive API tester for teamAuth backend"""
    
    def __init__(self, base_url: str = "http://localhost:8080/api/v1"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.tokens = {}
        self.test_data = {}
        
        # Test user credentials
        self.test_users = [
            {"email": "<EMAIL>", "password": "testpass123", "name": "Test User 1"},
            {"email": "<EMAIL>", "password": "testpass123", "name": "Test User 2"},
            {"email": "<EMAIL>", "password": "testpass123", "name": "Test User 3"}
        ]
        
    def log_test_result(self, endpoint: str, method: str, status: str, 
                       status_code: int = None, message: str = "", 
                       response_data: Any = None, error: str = ""):
        """Log test result"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "endpoint": endpoint,
            "method": method,
            "status": status,  # PASS, FAIL, SKIP
            "status_code": status_code,
            "message": message,
            "error": error,
            "response_data": response_data
        }
        self.test_results.append(result)
        
        status_symbol = "[PASS]" if status == "PASS" else "[FAIL]" if status == "FAIL" else "[SKIP]"
        logger.info(f"{status_symbol} {method} {endpoint} - {status}: {message}")
        if error:
            logger.error(f"   Error: {error}")
    
    def make_request(self, method: str, endpoint: str, data: Dict = None, 
                    headers: Dict = None, params: Dict = None, 
                    auth_token: str = None) -> requests.Response:
        """Make HTTP request with proper error handling"""
        url = f"{self.base_url}{endpoint}"
        
        # Set up headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
        if auth_token:
            request_headers["Authorization"] = f"Bearer {auth_token}"
            
        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=request_headers, params=params)
            elif method.upper() == "POST":
                response = self.session.post(url, headers=request_headers, 
                                           json=data, params=params)
            elif method.upper() == "PUT":
                response = self.session.put(url, headers=request_headers, 
                                          json=data, params=params)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=request_headers, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed for {method} {endpoint}: {str(e)}")
            raise
    
    def test_endpoint(self, method: str, endpoint: str, data: Dict = None,
                     auth_token: str = None, expected_status: int = 200,
                     test_name: str = "", params: Dict = None) -> Optional[Dict]:
        """Test a single endpoint"""
        try:
            response = self.make_request(method, endpoint, data, 
                                       auth_token=auth_token, params=params)
            
            # Parse response
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = response.text
            
            # Check status code
            if response.status_code == expected_status:
                self.log_test_result(endpoint, method, "PASS", 
                                   response.status_code, 
                                   test_name or f"Expected {expected_status}",
                                   response_data)
                return response_data
            else:
                self.log_test_result(endpoint, method, "FAIL", 
                                   response.status_code,
                                   test_name or f"Expected {expected_status}, got {response.status_code}",
                                   response_data)
                return None
                
        except Exception as e:
            self.log_test_result(endpoint, method, "FAIL", 
                               message=test_name or "Request failed",
                               error=str(e))
            return None
    
    def setup_test_data(self):
        """Set up test data by registering users and creating teams"""
        logger.info("Setting up test data...")

        # Register test users
        for i, user in enumerate(self.test_users):
            response = self.test_endpoint("POST", "/auth/register", user,
                                        expected_status=200,
                                        test_name=f"Register test user {i+1}")
            if response:
                logger.info(f"Registered user: {user['email']}")
            else:
                # User might already exist, try to login
                login_response = self.test_endpoint("POST", "/auth/login",
                                                  {"email": user["email"], "password": user["password"]},
                                                  expected_status=200,
                                                  test_name=f"Login existing user {i+1}")
                if login_response and 'data' in login_response:
                    self.tokens[f"user_{i+1}"] = login_response['data']['token']
                    logger.info(f"Logged in existing user: {user['email']}")

        # Login users to get tokens
        for i, user in enumerate(self.test_users):
            if f"user_{i+1}" not in self.tokens:
                login_response = self.test_endpoint("POST", "/auth/login",
                                                  {"email": user["email"], "password": user["password"]},
                                                  expected_status=200,
                                                  test_name=f"Login user {i+1}")
                if login_response and 'data' in login_response:
                    self.tokens[f"user_{i+1}"] = login_response['data']['token']

        logger.info(f"Setup complete. Tokens obtained: {list(self.tokens.keys())}")
    
    def run_all_tests(self):
        """Run all API tests"""
        logger.info("Starting comprehensive API tests...")
        start_time = time.time()

        # Setup test data
        self.setup_test_data()

        # Run test suites
        self.test_authentication_endpoints()
        self.test_subscription_endpoints()
        self.test_team_management_endpoints()
        self.test_user_management_endpoints()
        self.test_friend_management_endpoints()

        # Generate report
        self.generate_test_report()

        end_time = time.time()
        logger.info(f"All tests completed in {end_time - start_time:.2f} seconds")
    
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        logger.info("Testing Authentication Endpoints...")

        # Test user registration (already done in setup, test validation)
        self.test_endpoint("POST", "/auth/register",
                         {"email": "invalid-email", "password": "123", "name": "Test"},
                         expected_status=400,
                         test_name="Register with invalid data")

        # Test login with invalid credentials
        self.test_endpoint("POST", "/auth/login",
                         {"email": "<EMAIL>", "password": "wrongpass"},
                         expected_status=400,
                         test_name="Login with invalid credentials")

        # Test token validation
        if "user_1" in self.tokens:
            self.test_endpoint("GET", "/auth/validate",
                             auth_token=self.tokens["user_1"],
                             expected_status=200,
                             test_name="Validate valid token")

        # Test token validation with invalid token
        self.test_endpoint("GET", "/auth/validate",
                         auth_token="invalid_token",
                         expected_status=401,
                         test_name="Validate invalid token")

        # Test refresh token
        if "user_1" in self.tokens:
            self.test_endpoint("POST", "/auth/refresh-token",
                             auth_token=self.tokens["user_1"],
                             expected_status=200,
                             test_name="Refresh valid token")

        # Test logout (but keep a backup token for other tests)
        if "user_1" in self.tokens:
            # Create a backup token by logging in again with user_1
            login_response = self.test_endpoint("POST", "/auth/login",
                                              {"email": self.test_users[0]["email"], "password": self.test_users[0]["password"]},
                                              expected_status=200,
                                              test_name="Login user 1 for backup token")
            if login_response and 'data' in login_response:
                backup_token = login_response['data']['token']

                # Save the original token for logout test
                original_token = self.tokens["user_1"]

                # Replace with backup token before testing logout
                self.tokens["user_1"] = backup_token

                # Test logout with the original token (not the one we'll use for future tests)
                self.test_endpoint("POST", "/auth/logout",
                                 auth_token=original_token,
                                 expected_status=200,
                                 test_name="Logout with valid token")

    def test_subscription_endpoints(self):
        """Test subscription endpoints"""
        logger.info("Testing Subscription Endpoints...")

        # Test get subscription plans (public endpoint)
        plans_response = self.test_endpoint("GET", "/plans",
                                          expected_status=200,
                                          test_name="Get subscription plans")

        if plans_response and 'data' in plans_response:
            self.test_data['subscription_plans'] = plans_response['data']

        # Test authenticated subscription endpoints
        if "user_1" in self.tokens:
            # Get user subscriptions
            self.test_endpoint("GET", "/subscriptions",
                             auth_token=self.tokens["user_1"],
                             expected_status=200,
                             test_name="Get user subscriptions")

            # Get current subscription
            self.test_endpoint("GET", "/subscriptions/current",
                             auth_token=self.tokens["user_1"],
                             expected_status=200,
                             test_name="Get current subscription")

            # Test create subscription (if we have subscription plans)
            if 'subscription_plans' in self.test_data and self.test_data['subscription_plans']:
                plan_id = self.test_data['subscription_plans'][0].get('id')
                if plan_id:
                    subscription_data = {"planId": plan_id}
                    create_sub_response = self.test_endpoint("POST", "/subscriptions", subscription_data,
                                                           auth_token=self.tokens["user_1"],
                                                           expected_status=200,
                                                           test_name="Create subscription")

                    # Test cancel subscription (if created successfully)
                    if create_sub_response and 'data' in create_sub_response:
                        subscription_id = create_sub_response['data'].get('id')
                        if subscription_id:
                            self.test_endpoint("DELETE", f"/subscriptions/{subscription_id}",
                                             auth_token=self.tokens["user_1"],
                                             expected_status=200,
                                             test_name="Cancel subscription")

    def test_team_management_endpoints(self):
        """Test team management endpoints"""
        logger.info("Testing Team Management Endpoints...")

        if "user_1" not in self.tokens:
            logger.warning("Skipping team tests - no user token available")
            return

        # Test get user teams
        teams_response = self.test_endpoint("GET", "/teams",
                                          auth_token=self.tokens["user_1"],
                                          expected_status=200,
                                          test_name="Get user teams")

        # Test create team with unique name
        import time
        unique_suffix = str(int(time.time()))
        team_data = {"name": f"Test Team API {unique_suffix}", "description": "Team created by API test"}
        create_response = self.test_endpoint("POST", "/teams", team_data,
                                           auth_token=self.tokens["user_1"],
                                           expected_status=200,
                                           test_name="Create new team")

        if create_response and 'data' in create_response:
            team_id = create_response['data'].get('id')
            self.test_data['created_team_id'] = team_id

            # Select the created team to get team token
            select_response = self.test_endpoint("POST", "/auth/select-team",
                                               {"teamId": team_id},
                                               auth_token=self.tokens["user_1"],
                                               expected_status=200,
                                               test_name="Select created team")

            if select_response and 'data' in select_response:
                self.tokens["team_1"] = select_response['data']['token']

                # Test team-specific endpoints with team token
                self.test_team_specific_endpoints()

    def test_team_specific_endpoints(self):
        """Test endpoints that require team token"""
        if "team_1" not in self.tokens:
            logger.warning("Skipping team-specific tests - no team token available")
            return

        # Test get current team details
        self.test_endpoint("GET", "/teams/current",
                         auth_token=self.tokens["team_1"],
                         expected_status=200,
                         test_name="Get current team details")

        # Test update team info
        update_data = {"name": "Updated Test Team", "description": "Updated description"}
        self.test_endpoint("PUT", "/teams/current", update_data,
                         auth_token=self.tokens["team_1"],
                         expected_status=200,
                         test_name="Update team information")

        # Test get team members
        self.test_endpoint("GET", "/teams/current/members",
                         auth_token=self.tokens["team_1"],
                         expected_status=200,
                         test_name="Get team members")

        # Test invite members by email
        invite_data = {"emails": ["<EMAIL>"]}
        self.test_endpoint("POST", "/teams/current/members/invite", invite_data,
                         auth_token=self.tokens["team_1"],
                         expected_status=200,
                         test_name="Invite team members")

        # Test delete team (commented out to avoid deleting test data)
        # self.test_endpoint("DELETE", "/teams/current",
        #                  auth_token=self.tokens["team_1"],
        #                  expected_status=200,
        #                  test_name="Delete current team")

    def test_user_management_endpoints(self):
        """Test user management endpoints"""
        logger.info("Testing User Management Endpoints...")

        if "user_1" not in self.tokens:
            logger.warning("Skipping user tests - no user token available")
            return

        # Test get user profile
        profile_response = self.test_endpoint("GET", "/users/profile",
                                            auth_token=self.tokens["user_1"],
                                            expected_status=200,
                                            test_name="Get user profile")

        # Test update user profile
        update_data = {"name": "Updated Test User"}
        self.test_endpoint("PUT", "/users/profile", update_data,
                         auth_token=self.tokens["user_1"],
                         expected_status=200,
                         test_name="Update user profile")

        # Test validate password
        password_data = {"password": "testpass123"}
        self.test_endpoint("POST", "/users/validate-password", password_data,
                         auth_token=self.tokens["user_1"],
                         expected_status=200,
                         test_name="Validate user password")

        # Test validate wrong password (should return 400 for wrong password)
        wrong_password_data = {"password": "wrongpassword"}
        self.test_endpoint("POST", "/users/validate-password", wrong_password_data,
                         auth_token=self.tokens["user_1"],
                         expected_status=400,
                         test_name="Validate wrong password (should fail)")

    def test_friend_management_endpoints(self):
        """Test friend management endpoints"""
        logger.info("Testing Friend Management Endpoints...")

        if "user_1" not in self.tokens or "user_2" not in self.tokens:
            logger.warning("Skipping friend tests - insufficient user tokens")
            return

        # Test send friend request (might fail if already exists)
        friend_request_data = {"email": self.test_users[1]["email"]}
        friend_request_response = self.test_endpoint("POST", "/friends/request", friend_request_data,
                                                   auth_token=self.tokens["user_1"],
                                                   expected_status=200,
                                                   test_name="Send friend request")

        # If friend request already exists, that's expected behavior
        if not friend_request_response:
            self.test_endpoint("POST", "/friends/request", friend_request_data,
                             auth_token=self.tokens["user_1"],
                             expected_status=400,
                             test_name="Send friend request (already exists - expected)")

        # Test get sent friend requests (known issue - may fail with 500)
        sent_requests = self.test_endpoint("GET", "/friends/requests/sent",
                                         auth_token=self.tokens["user_1"],
                                         expected_status=200,
                                         test_name="Get sent friend requests (known DB issue)")

        # Test get received friend requests (from user_2's perspective) (known issue - may fail with 500)
        received_requests = self.test_endpoint("GET", "/friends/requests/received",
                                             auth_token=self.tokens["user_2"],
                                             expected_status=200,
                                             test_name="Get received friend requests (known DB issue)")

        # Test accept friend request (if any received)
        if received_requests and 'data' in received_requests and received_requests['data']:
            request_id = received_requests['data'][0].get('id')
            if request_id:
                self.test_endpoint("POST", f"/friends/accept/{request_id}",
                                 auth_token=self.tokens["user_2"],
                                 expected_status=200,
                                 test_name="Accept friend request")

                # Test reject friend request (create another request first)
                friend_request_data2 = {"email": self.test_users[2]["email"]}
                self.test_endpoint("POST", "/friends/request", friend_request_data2,
                                 auth_token=self.tokens["user_1"],
                                 expected_status=200,
                                 test_name="Send friend request to user 3")

                # Get received requests for user 3 and reject one
                if "user_3" in self.tokens:
                    received_requests_3 = self.test_endpoint("GET", "/friends/requests/received",
                                                           auth_token=self.tokens["user_3"],
                                                           expected_status=200,
                                                           test_name="Get received friend requests for user 3")

                    if received_requests_3 and 'data' in received_requests_3 and received_requests_3['data']:
                        request_id_3 = received_requests_3['data'][0].get('id')
                        if request_id_3:
                            self.test_endpoint("POST", f"/friends/reject/{request_id_3}",
                                             auth_token=self.tokens["user_3"],
                                             expected_status=200,
                                             test_name="Reject friend request")

        # Test get friends list
        self.test_endpoint("GET", "/friends/list",
                         auth_token=self.tokens["user_1"],
                         expected_status=200,
                         test_name="Get friends list")

        # Test additional friend management endpoints

        # Test get friend count
        self.test_endpoint("GET", "/friends/count",
                         auth_token=self.tokens["user_1"],
                         expected_status=200,
                         test_name="Get friend count")

        # Test check friendship status
        if "user_2" in self.tokens:
            # Get user 2's profile to get their ID
            user2_profile = self.test_endpoint("GET", "/users/profile",
                                             auth_token=self.tokens["user_2"],
                                             expected_status=200,
                                             test_name="Get user 2 profile for friendship check")

            if user2_profile and 'data' in user2_profile:
                user2_id = user2_profile['data'].get('id')
                if user2_id:
                    self.test_endpoint("GET", "/friends/check",
                                     params={"userId": user2_id},
                                     auth_token=self.tokens["user_1"],
                                     expected_status=200,
                                     test_name="Check friendship status")

        # Test set friend remark (if we have friends)
        friends_list = self.test_endpoint("GET", "/friends/list",
                                        auth_token=self.tokens["user_1"],
                                        expected_status=200,
                                        test_name="Get friends list for remark test")

        if friends_list and 'data' in friends_list and friends_list['data']:
            friend_id = friends_list['data'][0].get('id')
            if friend_id:
                remark_data = {"friendId": friend_id, "remark": "Test Friend Remark"}
                self.test_endpoint("POST", "/friends/remark", remark_data,
                                 auth_token=self.tokens["user_1"],
                                 expected_status=200,
                                 test_name="Set friend remark")

                # Test get friend remark
                self.test_endpoint("GET", "/friends/remark",
                                 params={"friendId": friend_id},
                                 auth_token=self.tokens["user_1"],
                                 expected_status=200,
                                 test_name="Get friend remark")

        # Test send friend request to non-existent user
        invalid_request_data = {"email": "<EMAIL>"}
        self.test_endpoint("POST", "/friends/request", invalid_request_data,
                         auth_token=self.tokens["user_1"],
                         expected_status=400,
                         test_name="Send friend request to non-existent user")

    def generate_test_report(self):
        """Generate comprehensive test report"""
        logger.info("Generating test report...")

        # Calculate statistics
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        skipped_tests = len([r for r in self.test_results if r['status'] == 'SKIP'])

        # Generate summary
        summary = {
            "test_run_timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "skipped": skipped_tests,
            "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
            "base_url": self.base_url
        }

        # Group results by endpoint category
        categories = {
            "Authentication": [r for r in self.test_results if r['endpoint'].startswith('/auth')],
            "Team Management": [r for r in self.test_results if r['endpoint'].startswith('/teams')],
            "User Management": [r for r in self.test_results if r['endpoint'].startswith('/users')],
            "Friend Management": [r for r in self.test_results if r['endpoint'].startswith('/friends')],
            "Subscriptions": [r for r in self.test_results if r['endpoint'].startswith('/plans') or r['endpoint'].startswith('/subscriptions')]
        }

        # Create detailed report
        report = {
            "summary": summary,
            "categories": {},
            "detailed_results": self.test_results
        }

        for category, results in categories.items():
            if results:
                cat_passed = len([r for r in results if r['status'] == 'PASS'])
                cat_total = len(results)
                report["categories"][category] = {
                    "total": cat_total,
                    "passed": cat_passed,
                    "failed": len([r for r in results if r['status'] == 'FAIL']),
                    "success_rate": f"{(cat_passed/cat_total*100):.1f}%" if cat_total > 0 else "0%"
                }

        # Save report to file
        report_filename = f"api_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # Print summary to console
        print("\n" + "="*80)
        print("🎯 API TEST RESULTS SUMMARY")
        print("="*80)
        print(f"📅 Test Run: {summary['test_run_timestamp']}")
        print(f"🌐 Base URL: {summary['base_url']}")
        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⏭️ Skipped: {skipped_tests}")
        print(f"📈 Success Rate: {summary['success_rate']}")
        print("\n📋 Results by Category:")

        for category, stats in report["categories"].items():
            status_emoji = "✅" if stats["success_rate"] == "100.0%" else "⚠️" if float(stats["success_rate"].rstrip('%')) >= 50 else "❌"
            print(f"  {status_emoji} {category}: {stats['passed']}/{stats['total']} ({stats['success_rate']})")

        print(f"\n📄 Detailed report saved to: {report_filename}")
        print("📄 Detailed logs saved to: api_test_results.log")

        # Print failed tests
        failed_results = [r for r in self.test_results if r['status'] == 'FAIL']
        if failed_results:
            print("\n❌ FAILED TESTS:")
            print("-" * 80)
            for result in failed_results:
                print(f"  • {result['method']} {result['endpoint']}")
                print(f"    Status: {result['status_code']}, Message: {result['message']}")
                if result['error']:
                    print(f"    Error: {result['error']}")
                print()

        print("="*80)

        return report


def main():
    """Main execution function"""
    print("TeamAuth API Comprehensive Test Suite")
    print("="*50)

    # Check if backend is running
    tester = TeamAuthAPITester()
    try:
        response = requests.get(f"{tester.base_url}/plans", timeout=5)
        print(f"Backend is accessible at {tester.base_url}")
    except requests.exceptions.RequestException as e:
        print(f"Cannot connect to backend at {tester.base_url}")
        print(f"   Error: {e}")
        print("   Please ensure the backend server is running on port 8080")
        sys.exit(1)

    # Run all tests
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        tester.generate_test_report()
        sys.exit(1)
    except Exception as e:
        print(f"\nTest execution failed: {e}")
        logger.error(f"Test execution failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
