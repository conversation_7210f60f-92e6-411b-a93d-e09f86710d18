#!/usr/bin/env python3
"""
Debug script to check token issues
"""

import requests
import json

BASE_URL = "http://localhost:8080/api/v1"

def test_token_flow():
    """Test the complete token flow"""
    
    # 1. Register a user
    register_data = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "name": "Debug User"
    }
    
    print("1. Testing registration...")
    response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
    print(f"   Status: {response.status_code}")
    if response.status_code != 200:
        print(f"   Response: {response.text}")
    
    # 2. Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    print("\n2. Testing login...")
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        token = data['data']['token']
        print(f"   Token obtained: {token[:50]}...")
        
        # 3. Test token validation
        print("\n3. Testing token validation...")
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/auth/validate", headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        # 4. Test user profile endpoint
        print("\n4. Testing user profile...")
        response = requests.get(f"{BASE_URL}/users/profile", headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        # 5. Test friend request endpoint
        print("\n5. Testing friend request...")
        friend_data = {"email": "<EMAIL>"}
        response = requests.post(f"{BASE_URL}/friends/request", json=friend_data, headers=headers)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
    else:
        print(f"   Login failed: {response.text}")

if __name__ == "__main__":
    test_token_flow()
