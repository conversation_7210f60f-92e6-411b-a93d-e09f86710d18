#!/usr/bin/env python3
"""
Debug script to test entity conversion issues
"""

import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>

def test_entity_conversion():
    """Test if there are issues with entity field conversion"""
    try:
        # Connect to database
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            database='team_manage',
            user='root',
            password='a228702862.'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Check the exact data in the database
            print("1. Checking raw database data...")
            cursor.execute("SELECT id, account_id, invited_by, status, is_deleted, created_at FROM account_relation WHERE status = 'pending'")
            records = cursor.fetchall()
            
            if records:
                print(f"Found {len(records)} pending records:")
                for record in records:
                    print(f"   ID: {record[0]}, account_id: {record[1]}, invited_by: {record[2]}, status: {record[3]}, is_deleted: {record[4]}, created_at: {record[5]}")
            else:
                print("   No pending records found")
            
            # Check if there are any records with NULL values that might cause issues
            print("\n2. Checking for NULL values...")
            cursor.execute("SELECT COUNT(*) FROM account_relation WHERE status IS NULL OR created_at IS NULL")
            null_count = cursor.fetchone()[0]
            print(f"   Records with NULL status or created_at: {null_count}")
            
            # Check the exact column types
            print("\n3. Checking column types...")
            cursor.execute("DESCRIBE account_relation")
            columns = cursor.fetchall()
            
            for column in columns:
                if column[0] in ['status', 'created_at', 'is_deleted']:
                    print(f"   {column[0]}: {column[1]} (Null: {column[2]}, Default: {column[4]})")
            
            # Test a simple query that should work
            print("\n4. Testing simple query...")
            cursor.execute("SELECT * FROM account_relation WHERE invited_by = 17 LIMIT 1")
            result = cursor.fetchone()
            if result:
                print(f"   Found record: {result}")
            else:
                print("   No records found for user 17")
                
    except Error as e:
        print(f"Database connection failed: {e}")
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    test_entity_conversion()
