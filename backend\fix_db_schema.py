#!/usr/bin/env python3
"""
Fix database schema by adding missing created_at column
"""

import mysql.connector
from mysql.connector import <PERSON><PERSON>r

def fix_database_schema():
    """Add missing created_at column to account_relation table"""
    try:
        # Connect to database
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            database='team_manage',
            user='root',
            password='a228702862.'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Add created_at column
            print("Adding created_at column to account_relation table...")
            try:
                alter_query = """
                ALTER TABLE account_relation 
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP 
                AFTER is_deleted
                """
                cursor.execute(alter_query)
                connection.commit()
                print("✓ created_at column added successfully!")
                
            except Error as e:
                if "Duplicate column name" in str(e):
                    print("✓ created_at column already exists")
                else:
                    print(f"✗ Failed to add created_at column: {e}")
                    return
            
            # Verify the table structure
            print("\nVerifying table structure...")
            cursor.execute("DESCRIBE account_relation")
            columns = cursor.fetchall()
            
            has_created_at = False
            has_updated_at = False
            
            for column in columns:
                if column[0] == 'created_at':
                    has_created_at = True
                if column[0] == 'updated_at':
                    has_updated_at = True
                print(f"   {column[0]}: {column[1]}")
            
            if has_created_at and has_updated_at:
                print("\n✓ Table structure is now correct!")
            else:
                print(f"\n✗ Missing columns: created_at={has_created_at}, updated_at={has_updated_at}")
            
    except Error as e:
        print(f"Database connection failed: {e}")
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    fix_database_schema()
