#!/usr/bin/env python3
"""
Debug script to test friend request database queries
"""

import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>

def debug_friend_queries():
    """Debug friend request database queries"""
    try:
        # Connect to database
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            database='team_manage',
            user='root',
            password='a228702862.'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Check existing records
            print("1. Checking existing account_relation records...")
            cursor.execute("SELECT * FROM account_relation")
            records = cursor.fetchall()
            
            if records:
                print(f"Found {len(records)} records:")
                for record in records:
                    print(f"   ID: {record[0]}, account_id: {record[1]}, invited_by: {record[2]}, status: {record[5]}")
            else:
                print("   No records found")
            
            # Test the specific queries that are failing
            print("\n2. Testing sent friend requests query...")
            user_id = 17  # debug user ID
            try:
                query = """
                SELECT * FROM account_relation 
                WHERE invited_by = %s AND status = 'pending' AND is_deleted = 0 
                ORDER BY requested_at DESC
                """
                cursor.execute(query, (user_id,))
                results = cursor.fetchall()
                print(f"   Found {len(results)} sent requests for user {user_id}")
                for result in results:
                    print(f"     {result}")
            except Error as e:
                print(f"   Query failed: {e}")
            
            print("\n3. Testing received friend requests query...")
            try:
                query = """
                SELECT * FROM account_relation 
                WHERE account_id = %s AND status = 'pending' AND is_deleted = 0 
                ORDER BY requested_at DESC
                """
                cursor.execute(query, (user_id,))
                results = cursor.fetchall()
                print(f"   Found {len(results)} received requests for user {user_id}")
                for result in results:
                    print(f"     {result}")
            except Error as e:
                print(f"   Query failed: {e}")
            
            # Test with different user IDs
            print("\n4. Testing with other user IDs...")
            cursor.execute("SELECT id FROM account LIMIT 5")
            user_ids = cursor.fetchall()
            
            for user_id_tuple in user_ids:
                user_id = user_id_tuple[0]
                cursor.execute("SELECT COUNT(*) FROM account_relation WHERE invited_by = %s", (user_id,))
                sent_count = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM account_relation WHERE account_id = %s", (user_id,))
                received_count = cursor.fetchone()[0]
                print(f"   User {user_id}: sent={sent_count}, received={received_count}")
            
    except Error as e:
        print(f"Database connection failed: {e}")
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    debug_friend_queries()
