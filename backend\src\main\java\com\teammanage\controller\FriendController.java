package com.teammanage.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.AddFriendRequest;
import com.teammanage.dto.request.SetFriendRemarkRequest;
import com.teammanage.entity.Account;
import com.teammanage.entity.AccountRelation;
import com.teammanage.service.FriendService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 好友关系管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/friends")
@Tag(name = "好友管理", description = "好友关系管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class FriendController {

    private static final Logger log = LoggerFactory.getLogger(FriendController.class);

    @Autowired
    private FriendService friendService;

    @Autowired
    private com.teammanage.mapper.AccountRelationMapper accountRelationMapper;

    /**
     * 发送好友请求
     *
     * 接口功能：
     * - 通过邮箱地址发送好友请求
     * - 验证目标用户存在性
     * - 防止重复发送和自我添加
     *
     * 请求参数：
     * - email: 好友的邮箱地址（必填，格式验证）
     *
     * 返回结果：
     * - 成功：返回成功消息
     * - 失败：抛出业务异常（用户不存在、已是好友等）
     */
    @PostMapping("/request")
    @Operation(summary = "发送好友请求", description = "通过邮箱发送好友请求")
    public ApiResponse<String> sendFriendRequest(@Valid @RequestBody AddFriendRequest request) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.sendFriendRequest(currentUserId, request.getEmail());
        return ApiResponse.success("好友请求发送成功");
    }

    /**
     * 添加好友（兼容旧接口）
     * @deprecated 使用 sendFriendRequest 替代
     */
    @Deprecated
    @PostMapping("/add")
    @Operation(summary = "添加好友", description = "通过邮箱添加好友")
    public ApiResponse<String> addFriend(@Valid @RequestBody AddFriendRequest request) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.addFriend(currentUserId, request.getEmail());
        return ApiResponse.success("好友请求发送成功");
    }

    /**
     * 删除好友
     */
    @DeleteMapping("/remove")
    @Operation(summary = "删除好友", description = "删除指定好友关系")
    public ApiResponse<String> removeFriend(
            @Parameter(description = "好友ID") @RequestParam Long friendId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.removeFriend(currentUserId, friendId);
        return ApiResponse.success("好友删除成功");
    }

    /**
     * 接受好友请求
     */
    @PostMapping("/accept/{requestId}")
    @Operation(summary = "接受好友请求", description = "接受指定的好友请求")
    public ApiResponse<String> acceptFriendRequest(
            @Parameter(description = "好友请求ID") @PathVariable Long requestId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.acceptFriendRequest(currentUserId, requestId);
        return ApiResponse.success("好友请求已接受");
    }

    /**
     * 拒绝好友请求
     */
    @PostMapping("/reject/{requestId}")
    @Operation(summary = "拒绝好友请求", description = "拒绝指定的好友请求")
    public ApiResponse<String> rejectFriendRequest(
            @Parameter(description = "好友请求ID") @PathVariable Long requestId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.rejectFriendRequest(currentUserId, requestId);
        return ApiResponse.success("好友请求已拒绝");
    }

    /**
     * 取消好友请求
     */
    @DeleteMapping("/request/{requestId}")
    @Operation(summary = "取消好友请求", description = "取消自己发送的好友请求")
    public ApiResponse<String> cancelFriendRequest(
            @Parameter(description = "好友请求ID") @PathVariable Long requestId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.cancelFriendRequest(currentUserId, requestId);
        return ApiResponse.success("好友请求已取消");
    }

    /**
     * 获取好友列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取好友列表", description = "获取当前用户的所有好友")
    public ApiResponse<List<Account>> getFriends() {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<Account> friends = friendService.getFriends(currentUserId);
        return ApiResponse.success(friends);
    }

    /**
     * 获取收到的好友请求列表
     */
    @GetMapping("/requests/received")
    @Operation(summary = "获取收到的好友请求", description = "获取当前用户收到的待处理好友请求")
    public ApiResponse<List<AccountRelation>> getReceivedFriendRequests() {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<AccountRelation> requests = friendService.getReceivedFriendRequests(currentUserId);
        return ApiResponse.success(requests);
    }

    /**
     * 获取发送的好友请求列表
     */
    @GetMapping("/requests/sent")
    @Operation(summary = "获取发送的好友请求", description = "获取当前用户发送的待处理好友请求")
    public ApiResponse<List<AccountRelation>> getSentFriendRequests() {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<AccountRelation> requests = friendService.getSentFriendRequests(currentUserId);
        return ApiResponse.success(requests);
    }

    /**
     * 测试直接调用mapper方法
     */
    @GetMapping("/test/mapper")
    @Operation(summary = "测试mapper方法", description = "直接测试mapper方法")
    public ApiResponse<String> testMapper() {
        try {
            Long currentUserId = SecurityUtil.getCurrentUserId();
            // 先测试简单的count查询
            int count = accountRelationMapper.countReceivedFriendRequests(currentUserId);
            return ApiResponse.success("Success: count = " + count);
        } catch (Exception e) {
            return ApiResponse.error("Error: " + e.getClass().getSimpleName() + " - " + e.getMessage());
        }
    }

    /**
     * 检查好友关系
     */
    @GetMapping("/check")
    @Operation(summary = "检查好友关系", description = "检查与指定用户是否为好友关系")
    public ApiResponse<Boolean> checkFriendship(
            @Parameter(description = "用户ID") @RequestParam Long userId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        boolean isFriend = friendService.isFriend(currentUserId, userId);
        return ApiResponse.success(isFriend);
    }

    /**
     * 获取好友数量
     */
    @GetMapping("/count")
    @Operation(summary = "获取好友数量", description = "获取当前用户的好友总数")
    public ApiResponse<Integer> getFriendCount() {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        int count = friendService.getFriendCount(currentUserId);
        return ApiResponse.success(count);
    }

    /**
     * 设置好友备注
     */
    @PostMapping("/remark")
    @Operation(summary = "设置好友备注", description = "为指定好友设置备注")
    public ApiResponse<String> setFriendRemark(@Valid @RequestBody SetFriendRemarkRequest request) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        friendService.setFriendRemark(currentUserId, request.getFriendId(), request.getRemark());
        return ApiResponse.success("好友备注设置成功");
    }

    /**
     * 获取好友备注
     */
    @GetMapping("/remark")
    @Operation(summary = "获取好友备注", description = "获取指定好友的备注")
    public ApiResponse<String> getFriendRemark(
            @Parameter(description = "好友ID") @RequestParam Long friendId) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        String remark = friendService.getFriendRemark(currentUserId, friendId);
        return ApiResponse.success(remark);
    }
}
